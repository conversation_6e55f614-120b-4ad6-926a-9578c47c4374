<template>
  <div class="rich-text-editor">
    <div v-if="editor" class="editor-container">
      <!-- Toolbar -->
      <div class="toolbar">
        <!-- Undo/Redo -->
        <div class="toolbar-group">
          <button class="icon-btn" @click="editor.chain().focus().undo().run()" :disabled="!editor.can().chain().focus().undo().run()" title="Undo">↶</button>
          <button class="icon-btn" @click="editor.chain().focus().redo().run()" :disabled="!editor.can().chain().focus().redo().run()" title="Redo">↷</button>
        </div>

        <!-- Headings dropdown -->
        <div class="toolbar-group dropdown" @mouseleave="showHeading = false">
          <button class="icon-btn" :class="{ 'is-active': editor.isActive('heading') }" @click="toggleHeadingMenu" title="Headings">
            H<span class="caret">▾</span>
          </button>
          <div v-if="showHeading" class="dropdown-menu">
            <button class="menu-item" @click="setHeading(1)">H1 Heading 1</button>
            <button class="menu-item" @click="setHeading(2)">H2 Heading 2</button>
            <button class="menu-item" @click="setHeading(3)">H3 Heading 3</button>
            <button class="menu-item" @click="setHeading(4)">H4 Heading 4</button>
          </div>
        </div>

        <!-- Lists dropdown -->
        <div class="toolbar-group dropdown" @mouseleave="showList = false">
          <button class="icon-btn" :class="{ 'is-active': editor.isActive('bulletList') || editor.isActive('orderedList') }" @click="toggleListMenu" title="Lists">
            ≡<span class="caret">▾</span>
          </button>
          <div v-if="showList" class="dropdown-menu">
            <button class="menu-item" @click="editor.chain().focus().toggleBulletList().run()">• Bullet List</button>
            <button class="menu-item" @click="editor.chain().focus().toggleOrderedList().run()">1. Ordered List</button>
          </div>
        </div>

        <!-- Inline styles -->
        <div class="toolbar-group">
          <button class="icon-btn" @click="editor.chain().focus().toggleBold().run()" :class="{ 'is-active': editor.isActive('bold') }" title="Bold">B</button>
          <button class="icon-btn" @click="editor.chain().focus().toggleItalic().run()" :class="{ 'is-active': editor.isActive('italic') }" title="Italic">I</button>
          <button class="icon-btn" @click="editor.chain().focus().toggleStrike().run()" :class="{ 'is-active': editor.isActive('strike') }" title="Strikethrough">S</button>
          <button class="icon-btn" @click="editor.chain().focus().toggleCode().run()" :class="{ 'is-active': editor.isActive('code') }" title="Code">&lt;/&gt;</button>
        </div>

        <!-- Insert -->
        <div class="toolbar-group">
          <button class="icon-btn" @click="addImage" title="Insert Image">🖼️</button>
          <button class="icon-btn" @click="addVideo" title="Insert Video">🎬</button>
          <button class="icon-btn" @click="addTable" title="Insert Table">▦</button>
        </div>

        <!-- Fullscreen -->
        <div class="toolbar-group right">
          <button class="icon-btn" @click="toggleFullscreen" title="Toggle Fullscreen">⤢</button>
        </div>
      </div>

      <!-- Editor Content -->
      <editor-content :editor="editor" class="editor-content" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import { TableKit } from '@tiptap/extension-table'
import Image from '@tiptap/extension-image'
import { ImageResize } from 'tiptap-extension-resize-image'
import { Video } from './extensions/video.js'

// Editor instance
const editor = ref(null)

// UI states
const isFullscreen = ref(false)
const showHeading = ref(false)
const showList = ref(false)

// Initialize editor
onMounted(() => {
  editor.value = new Editor({
    extensions: [
      StarterKit,
      TableKit.configure({
        table: { resizable: true },
        tableCell: true,
      }),
      Image,
      ImageResize,
      Video
    ],
    content: `
      <h1>Rich Text Editor</h1>
      <p>This is a <strong>rich text editor</strong> built with TipTap v3.</p>
      <p>You can add <em>text formatting</em>, <u>tables</u>, <strong>images</strong>, and <strong>videos</strong>.</p>
    `,
  })
})

// Clean up editor
onUnmounted(() => {
  if (editor.value) editor.value.destroy()
})

// Helper actions
const setHeading = (level) => {
  editor.value.chain().focus().toggleHeading({ level }).run()
  showHeading.value = false
}
const toggleHeadingMenu = () => {
  showHeading.value = !showHeading.value
  showList.value = false
}
const toggleListMenu = () => {
  showList.value = !showList.value
  showHeading.value = false
}

// Insert actions
const addTable = () => {
  editor.value.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()
}
const addImage = () => {
  const url = prompt('Enter image URL:')
  if (url) editor.value.chain().focus().setImage({ src: url }).run()
}
const addVideo = () => {
  const url = prompt('Enter video URL (YouTube, Vimeo, etc.):')
  if (!url) return
  if (url.includes('youtube.com') || url.includes('youtu.be')) {
    let videoId = ''
    if (url.includes('youtube.com/watch?v=')) {
      videoId = url.split('v=')[1]?.split('&')[0]
    } else if (url.includes('youtu.be/')) {
      videoId = url.split('youtu.be/')[1]?.split('?')[0]
    }
    if (videoId) {
      editor.value.commands.insertContent({ type: 'video', attrs: { src: `https://www.youtube.com/embed/${videoId}` } })
      return
    }
  }
  if (url.includes('vimeo.com')) {
    const videoId = url.split('vimeo.com/')[1]?.split('?')[0]
    if (videoId) {
      editor.value.commands.insertContent({ type: 'video', attrs: { src: `https://player.vimeo.com/video/${videoId}` } })
      return
    }
  }
  editor.value.commands.insertContent({ type: 'video', attrs: { src: url } })
}

// Fullscreen
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  if (isFullscreen.value) document.documentElement.requestFullscreen()
  else document.exitFullscreen()
}
</script>

<style scoped>
/* Container */
.rich-text-editor {
  border: 1px solid #e8e8ef;
  border-radius: 10px;
  overflow: hidden;
  background: #fff;
}

.editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Toolbar */
.toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 10px;
  border-bottom: 1px solid #eee;
  background-color: #fafafa;
}

.toolbar-group {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px;
  border: 1px solid #eee;
  border-radius: 8px;
  background: #fff;
}

.toolbar-group.right {
  margin-left: auto;
}

.icon-btn {
  width: 28px;
  height: 28px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 13px;
  line-height: 1;
  color: #4b5563;
  background: #fff;
  border: 1px solid #e6e6ef;
  border-radius: 6px;
  cursor: pointer;
  transition: background .15s ease, color .15s ease, border-color .15s ease, box-shadow .15s ease;
}

.icon-btn:hover {
  background: #f3f4f6;
}

.icon-btn.is-active {
  background: #f1edff;
  color: #5b48f1;
  border-color: #d9d2ff;
  box-shadow: 0 0 0 2px rgba(107, 70, 193, 0.12) inset;
}

.icon-btn:disabled {
  opacity: .5;
  cursor: not-allowed;
}

.caret {
  margin-left: 2px;
  font-size: 10px;
}

/* Dropdown */
.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 36px;
  left: 0;
  min-width: 180px;
  background: #fff;
  border: 1px solid #ececf4;
  box-shadow: 0 8px 20px rgba(0,0,0,.08);
  border-radius: 10px;
  padding: 6px;
  z-index: 20;
}

.menu-item {
  width: 100%;
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 8px 10px;
  border: 0;
  background: transparent;
  color: #111827;
  font-size: 13px;
  border-radius: 8px;
  cursor: pointer;
}

.menu-item:hover {
  background: #f6f6fb;
}

/* Editor content */
.editor-content {
  flex: 1;
  padding: 20px 24px;
  overflow-y: auto;
  min-height: 320px;
}

/* Fullscreen styles */
:fullscreen .rich-text-editor {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  border: none;
  border-radius: 0;
}

:fullscreen .editor-content {
  min-height: calc(100vh - 60px);
}
</style>

<style>
/* TipTap editor styles */
.tiptap {
  outline: none;
}

.tiptap p {
  margin: 0 0 1em 0;
}

.tiptap h1,
.tiptap h2,
.tiptap h3 {
  margin: 1.5em 0 0.5em 0;
  line-height: 1.2;
}

.tiptap h1 {
  font-size: 2em;
}

.tiptap h2 {
  font-size: 1.5em;
}

.tiptap h3 {
  font-size: 1.25em;
}

.tiptap ul,
.tiptap ol {
  padding-left: 1.5em;
  margin: 1em 0;
}

.tiptap li {
  margin: 0.25em 0;
}

.tiptap code {
  background-color: #f1f3f4;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
}

.tiptap pre {
  background-color: #f8f9fa;
  padding: 1em;
  border-radius: 4px;
  overflow-x: auto;
}

.tiptap pre code {
  background: none;
  padding: 0;
}

.tiptap img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 auto;
}

/* Table styles */
.tiptap table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin: 0;
}

.tiptap table td,
.tiptap table th {
  border: 1px solid #ced4da;
  box-sizing: border-box;
  min-width: 1em;
  padding: 3px 5px;
  position: relative;
  vertical-align: top;
}

.tiptap table td>*,
.tiptap table th>* {
  margin-bottom: 0;
}

.tiptap table th {
  background-color: #f1f3f4;
  font-weight: bold;
  text-align: left;
}

.tiptap table .selectedCell:after {
  background: rgba(200, 200, 255, 0.4);
  content: "";
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  pointer-events: none;
  position: absolute;
  z-index: 2;
}

.tiptap table .column-resize-handle {
  background-color: #adf;
  bottom: -2px;
  position: absolute;
  right: -2px;
  pointer-events: none;
  top: 0;
  width: 4px;
}

.tiptap table p {
  margin: 0;
}

/* Video styles */
.tiptap video {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 auto;
}

.resize-cursor {
  cursor: ew-resize;
  cursor: col-resize;
}
</style>
